<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 背景装饰弧线 -->
  <path d="M0,100 Q480,50 960,75 T1920,50" stroke="#e0e7ff" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,1000 Q480,950 960,975 T1920,950" stroke="#fde68a" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#7c3aed">
    实战演练：为你的典型客户"画像"
  </text>
  
  <!-- 任务说明 -->
  <rect x="100" y="130" width="1720" height="120" fill="#f3e8ff" rx="20" stroke="#8b5cf6" stroke-width="3"/>
  <text x="130" y="170" font-family="Microsoft YaHei" font-size="32" fill="#7c3aed" font-weight="bold">
    任务：(15-20分钟) 小组活动
  </text>
  <text x="130" y="210" font-family="Microsoft YaHei" font-size="26" fill="#6b21a8">
    各小组选择一位真实接触过的典型客户（或结合前面案例），参照学员手册中的Persona模板，
  </text>
  <text x="130" y="240" font-family="Microsoft YaHei" font-size="26" fill="#6b21a8">
    快速讨论并完成画像卡片绘制。要求：尽可能具体、生动，挖掘深层需求和痛点。
  </text>
  
  <!-- Persona模板 -->
  <g transform="translate(100,280)">
    <text x="0" y="30" font-family="Microsoft YaHei" font-size="36" fill="#1d4ed8" font-weight="bold">
      Persona模板要素：
    </text>
    
    <!-- 模板卡片 -->
    <rect x="0" y="50" width="1720" height="500" fill="#f8fafc" rx="20" stroke="#64748b" stroke-width="2"/>
    
    <!-- 左侧：基本信息区 -->
    <g transform="translate(50,100)">
      <!-- 头像区 -->
      <circle cx="80" cy="80" r="50" fill="#e2e8f0" stroke="#64748b" stroke-width="2"/>
      <text x="80" y="88" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#64748b">头像</text>
      
      <!-- 基本信息 -->
      <text x="160" y="50" font-family="Microsoft YaHei" font-size="24" fill="#1e3a8a" font-weight="bold">
        姓名：___________ (年龄：___岁)
      </text>
      <text x="160" y="80" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        职业：___________ 收入：___________
      </text>
      <text x="160" y="110" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        居住：___________ 家庭：___________
      </text>
      <text x="160" y="140" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        教育：___________ 其他：___________
      </text>
    </g>
    
    <!-- 右侧：标签区 -->
    <g transform="translate(900,100)">
      <text x="0" y="30" font-family="Microsoft YaHei" font-size="24" fill="#059669" font-weight="bold">
        典型标签：(用关键词描述)
      </text>
      
      <!-- 标签框 -->
      <rect x="0" y="40" width="700" height="120" fill="white" stroke="#10b981" stroke-width="1" rx="10"/>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        #_________ #_________ #_________ #_________
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        #_________ #_________ #_________ #_________
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        #_________ #_________ #_________ #_________
      </text>
    </g>
    
    <!-- 核心需求区 -->
    <g transform="translate(50,280)">
      <text x="0" y="30" font-family="Microsoft YaHei" font-size="24" fill="#d97706" font-weight="bold">
        核心需求：(客户最关心什么？最想要什么？)
      </text>
      
      <rect x="0" y="40" width="800" height="120" fill="white" stroke="#f59e0b" stroke-width="1" rx="10"/>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
    </g>
    
    <!-- 痛点区 -->
    <g transform="translate(900,280)">
      <text x="0" y="30" font-family="Microsoft YaHei" font-size="24" fill="#dc2626" font-weight="bold">
        痛点聚焦：(最困扰的问题是什么？)
      </text>
      
      <rect x="0" y="40" width="700" height="120" fill="white" stroke="#ef4444" stroke-width="1" rx="10"/>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        • _________________________________________________
      </text>
    </g>
    
    <!-- 触媒习惯区 -->
    <g transform="translate(50,440)">
      <text x="0" y="30" font-family="Microsoft YaHei" font-size="24" fill="#7c3aed" font-weight="bold">
        触媒习惯：(通过什么渠道获取信息？信任谁的推荐？)
      </text>
      
      <rect x="0" y="40" width="1550" height="80" fill="white" stroke="#8b5cf6" stroke-width="1" rx="10"/>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        信息渠道：_________________________________________________
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        决策影响：_________________________________________________
      </text>
    </g>
  </g>
  
  <!-- 工作指导 -->
  <rect x="200" y="820" width="1520" height="100" fill="#f0f9ff" rx="15" opacity="0.9"/>
  <text x="960" y="855" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#1d4ed8" font-weight="bold">
    工作指导：基于真实经验进行描绘，越具体越好！
  </text>
  <text x="960" y="885" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#3b82f6">
    讲师将巡视各小组，提供指导和启发。记住：画像是为了更好地服务客户！
  </text>
  
  <!-- 装饰性工具图标 -->
  <g transform="translate(1650,950)">
    <!-- 画笔 -->
    <line x1="0" y1="0" x2="30" y2="30" stroke="#7c3aed" stroke-width="3"/>
    <circle cx="0" cy="0" r="3" fill="#7c3aed"/>
    <path d="M25,25 L35,25 L35,35 L25,35 Z" fill="#fbbf24"/>
    
    <!-- 纸张 -->
    <rect x="40" y="5" width="25" height="35" fill="white" stroke="#64748b" stroke-width="1" rx="2"/>
    <line x1="45" y1="12" x2="60" y2="12" stroke="#e2e8f0" stroke-width="1"/>
    <line x1="45" y1="18" x2="60" y2="18" stroke="#e2e8f0" stroke-width="1"/>
    <line x1="45" y1="24" x2="60" y2="24" stroke="#e2e8f0" stroke-width="1"/>
    <line x1="45" y1="30" x2="60" y2="30" stroke="#e2e8f0" stroke-width="1"/>
  </g>
</svg>
