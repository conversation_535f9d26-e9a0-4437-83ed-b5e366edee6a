<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 背景装饰弧线 -->
  <path d="M0,100 Q480,50 960,75 T1920,50" stroke="#dcfce7" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,1000 Q480,950 960,975 T1920,950" stroke="#fde68a" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#10b981">
    家庭用户画像：关注网络、看重融合、向往智慧
  </text>
  
  <!-- 李女士一家Persona卡片 -->
  <g transform="translate(100,130)">
    <!-- 卡片背景 -->
    <rect x="0" y="0" width="1720" height="700" fill="#f0fdf4" rx="20" stroke="#10b981" stroke-width="3"/>
    
    <!-- 家庭头像区域 -->
    <g transform="translate(80,80)">
      <!-- 妈妈 -->
      <circle cx="0" cy="0" r="40" fill="#10b981"/>
      <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white" font-weight="bold">李</text>
      
      <!-- 爸爸 -->
      <circle cx="60" cy="0" r="35" fill="#059669"/>
      <text x="60" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white" font-weight="bold">王</text>
      
      <!-- 孩子 -->
      <circle cx="30" cy="50" r="25" fill="#34d399"/>
      <text x="30" y="58" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">小</text>
      
      <!-- 老人 -->
      <circle cx="100" cy="25" r="30" fill="#6ee7b7"/>
      <text x="100" y="33" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white" font-weight="bold">奶</text>
    </g>
    
    <!-- 基本信息 -->
    <text x="250" y="80" font-family="Microsoft YaHei" font-size="32" fill="#059669" font-weight="bold">
      李女士一家 (典型家庭用户)
    </text>
    <text x="250" y="115" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      家庭构成：夫妻+1孩+老人 | 居住：三居室 | 收入：家庭月收入15000-25000元
    </text>
    <text x="250" y="145" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      职业：李女士公司白领，先生工程师 | 孩子：小学生 | 老人：退休在家
    </text>
    <text x="250" y="175" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      居住情况：自有住房，装修3-5年，有改善网络需求
    </text>
    
    <!-- 典型标签 -->
    <text x="50" y="240" font-family="Microsoft YaHei" font-size="28" fill="#059669" font-weight="bold">
      典型标签：
    </text>
    
    <g transform="translate(50,260)">
      <!-- 标签行1 -->
      <rect x="0" y="0" width="120" height="35" fill="#10b981" rx="17"/>
      <text x="60" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#宽带核心</text>
      
      <rect x="140" y="0" width="120" height="35" fill="#059669" rx="17"/>
      <text x="200" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#全家共享</text>
      
      <rect x="280" y="0" width="100" height="35" fill="#047857" rx="17"/>
      <text x="330" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#性价比</text>
      
      <rect x="400" y="0" width="140" height="35" fill="#065f46" rx="17"/>
      <text x="470" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#智慧生活向往</text>
      
      <!-- 标签行2 -->
      <rect x="0" y="50" width="120" height="35" fill="#3b82f6" rx="17"/>
      <text x="60" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#孩子教育</text>
      
      <rect x="140" y="50" width="120" height="35" fill="#f59e0b" rx="17"/>
      <text x="200" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#精打细算</text>
      
      <rect x="280" y="50" width="120" height="35" fill="#ef4444" rx="17"/>
      <text x="340" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#品质生活</text>
      
      <rect x="420" y="50" width="120" height="35" fill="#8b5cf6" rx="17"/>
      <text x="480" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#安全第一</text>
    </g>
    
    <!-- 核心需求 -->
    <text x="50" y="370" font-family="Microsoft YaHei" font-size="28" fill="#059669" font-weight="bold">
      核心需求：
    </text>
    <text x="50" y="405" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 宽带要稳要快要全覆盖：FTTR全屋千兆！孩子学习、大人办公、老人娱乐都不卡
    </text>
    <text x="50" y="435" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 融合套餐要实惠要便捷：一个账单管全家，性价比要高，服务要省心
    </text>
    <text x="50" y="465" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 智家应用要好用要安全：看家护院、健康管理、教育辅助，但操作要简单
    </text>
    <text x="50" y="495" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 服务要专业要及时：有问题能快速解决，安装维修要靠谱
    </text>
    
    <!-- 痛点聚焦 -->
    <text x="900" y="240" font-family="Microsoft YaHei" font-size="28" fill="#dc2626" font-weight="bold">
      痛点聚焦：
    </text>
    <text x="900" y="275" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      Wi-Fi死角！速率不达标！
    </text>
    <text x="900" y="305" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      套餐看不懂！智家操作难！
    </text>
    <text x="900" y="335" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      安装维修慢！
    </text>
    
    <!-- 决策特点 -->
    <text x="900" y="390" font-family="Microsoft YaHei" font-size="28" fill="#059669" font-weight="bold">
      决策特点：
    </text>
    <text x="900" y="425" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 通常有主决策者(李女士)，但会综合全家需求
    </text>
    <text x="900" y="455" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 看重口碑和服务体验，朋友推荐很重要
    </text>
    <text x="900" y="485" font-family="Microsoft YaHei" font-size="22" fill="#047857">
      • 决策周期较长，会货比三家
    </text>
    
    <!-- FTTR效果图示意 -->
    <g transform="translate(1400,300)">
      <rect x="0" y="0" width="200" height="120" fill="none" stroke="#10b981" stroke-width="2" rx="10"/>
      <text x="100" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#059669" font-weight="bold">
        FTTR全屋覆盖
      </text>
      
      <!-- 房间示意 -->
      <rect x="20" y="30" width="40" height="30" fill="#dcfce7" rx="5"/>
      <text x="40" y="48" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#047857">客厅</text>
      
      <rect x="80" y="30" width="40" height="30" fill="#dcfce7" rx="5"/>
      <text x="100" y="48" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#047857">主卧</text>
      
      <rect x="140" y="30" width="40" height="30" fill="#dcfce7" rx="5"/>
      <text x="160" y="48" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#047857">次卧</text>
      
      <rect x="50" y="70" width="40" height="30" fill="#dcfce7" rx="5"/>
      <text x="70" y="88" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#047857">书房</text>
      
      <rect x="110" y="70" width="40" height="30" fill="#dcfce7" rx="5"/>
      <text x="130" y="88" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#047857">厨房</text>
      
      <!-- 信号覆盖示意 -->
      <circle cx="100" cy="60" r="50" fill="none" stroke="#10b981" stroke-width="1" opacity="0.5"/>
      <circle cx="100" cy="60" r="30" fill="none" stroke="#10b981" stroke-width="1" opacity="0.7"/>
      <circle cx="100" cy="60" r="10" fill="#10b981" opacity="0.3"/>
    </g>
    
    <!-- 互动提问 -->
    <rect x="50" y="580" width="1620" height="100" fill="#ecfdf5" rx="15" opacity="0.8"/>
    <text x="80" y="615" font-family="Microsoft YaHei" font-size="26" fill="#059669" font-weight="bold">
      互动提问：
    </text>
    <text x="80" y="650" font-family="Microsoft YaHei" font-size="24" fill="#047857">
      "和李女士这样的家庭用户打交道，你觉得最关键的是打动她的哪个点？"
    </text>
    <text x="80" y="680" font-family="Microsoft YaHei" font-size="22" fill="#065f46">
      (强调家庭用户的"整体性"需求和对"稳定"、"便捷"、"价值"的关注)
    </text>
  </g>
  
  <!-- 装饰性家庭图标 -->
  <g transform="translate(1650,900)">
    <!-- 房子 -->
    <path d="M0,30 L20,10 L40,30 L40,50 L0,50 Z" fill="#10b981"/>
    <rect x="5" y="35" width="30" height="15" fill="#dcfce7"/>
    <rect x="15" y="40" width="10" height="10" fill="#047857"/>
    <circle cx="12" cy="45" r="1" fill="#dcfce7"/>
    
    <!-- Wi-Fi信号 -->
    <path d="M50,35 Q55,30 60,35" stroke="#10b981" stroke-width="2" fill="none"/>
    <path d="M48,37 Q55,27 62,37" stroke="#10b981" stroke-width="2" fill="none"/>
    <path d="M46,39 Q55,24 64,39" stroke="#10b981" stroke-width="2" fill="none"/>
  </g>
</svg>
