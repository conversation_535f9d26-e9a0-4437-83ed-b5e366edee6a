<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 背景装饰弧线 -->
  <path d="M0,150 Q480,100 960,125 T1920,100" stroke="#e0e7ff" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,950 Q480,900 960,925 T1920,900" stroke="#fde68a" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#3b82f6">
    告别"凭感觉"，拥抱"数据化"识人术
  </text>
  
  <!-- 什么是用户画像 -->
  <rect x="100" y="150" width="1720" height="80" fill="#f0f9ff" rx="15" opacity="0.8"/>
  <text x="130" y="180" font-family="Microsoft YaHei" font-size="28" fill="#1d4ed8" font-weight="bold">
    什么是用户画像 (Persona)？
  </text>
  <text x="130" y="210" font-family="Microsoft YaHei" font-size="24" fill="#3b82f6">
    —— 给目标客户一个具体、生动的"脸谱"，基于真实数据构建的典型用户模型
  </text>
  
  <!-- 为什么要做用户画像 -->
  <g transform="translate(100,260)">
    <text x="0" y="30" font-family="Microsoft YaHei" font-size="32" fill="#059669" font-weight="bold">
      为什么要做用户画像？
    </text>
    
    <!-- 四个价值点 -->
    <g transform="translate(0,60)">
      <!-- 精准定位 -->
      <rect x="0" y="0" width="400" height="120" fill="#f0fdf4" rx="15" stroke="#10b981" stroke-width="2"/>
      <circle cx="60" cy="60" r="30" fill="#10b981"/>
      <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white" font-weight="bold">🎯</text>
      
      <text x="110" y="40" font-family="Microsoft YaHei" font-size="24" fill="#059669" font-weight="bold">
        精准定位
      </text>
      <text x="110" y="70" font-family="Microsoft YaHei" font-size="20" fill="#047857">
        明确目标客户群体
      </text>
      <text x="110" y="95" font-family="Microsoft YaHei" font-size="20" fill="#047857">
        避免盲目营销
      </text>
      
      <!-- 有效沟通 -->
      <rect x="420" y="0" width="400" height="120" fill="#f0f9ff" rx="15" stroke="#3b82f6" stroke-width="2"/>
      <circle cx="480" cy="60" r="30" fill="#3b82f6"/>
      <text x="480" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white" font-weight="bold">💬</text>
      
      <text x="530" y="40" font-family="Microsoft YaHei" font-size="24" fill="#1d4ed8" font-weight="bold">
        有效沟通
      </text>
      <text x="530" y="70" font-family="Microsoft YaHei" font-size="20" fill="#1e40af">
        用客户的语言对话
      </text>
      <text x="530" y="95" font-family="Microsoft YaHei" font-size="20" fill="#1e40af">
        提升沟通效果
      </text>
      
      <!-- 产品优化 -->
      <rect x="840" y="0" width="400" height="120" fill="#fefbf3" rx="15" stroke="#f59e0b" stroke-width="2"/>
      <circle cx="900" cy="60" r="30" fill="#f59e0b"/>
      <text x="900" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white" font-weight="bold">⚙️</text>
      
      <text x="950" y="40" font-family="Microsoft YaHei" font-size="24" fill="#d97706" font-weight="bold">
        产品优化
      </text>
      <text x="950" y="70" font-family="Microsoft YaHei" font-size="20" fill="#92400e">
        基于需求改进产品
      </text>
      <text x="950" y="95" font-family="Microsoft YaHei" font-size="20" fill="#92400e">
        提升产品适配度
      </text>
      
      <!-- 提升体验 -->
      <rect x="1260" y="0" width="400" height="120" fill="#fef2f2" rx="15" stroke="#ef4444" stroke-width="2"/>
      <circle cx="1320" cy="60" r="30" fill="#ef4444"/>
      <text x="1320" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white" font-weight="bold">⭐</text>
      
      <text x="1370" y="40" font-family="Microsoft YaHei" font-size="24" fill="#dc2626" font-weight="bold">
        提升体验
      </text>
      <text x="1370" y="70" font-family="Microsoft YaHei" font-size="20" fill="#991b1b">
        优化服务流程
      </text>
      <text x="1370" y="95" font-family="Microsoft YaHei" font-size="20" fill="#991b1b">
        增强用户满意度
      </text>
    </g>
  </g>
  
  <!-- 画像的核心维度 -->
  <g transform="translate(100,480)">
    <text x="0" y="30" font-family="Microsoft YaHei" font-size="32" fill="#7c3aed" font-weight="bold">
      画像的核心维度：
    </text>
    
    <!-- 四个维度图标 -->
    <g transform="translate(0,60)">
      <!-- 人口统计学 -->
      <rect x="0" y="0" width="400" height="100" fill="#f3e8ff" rx="15" stroke="#8b5cf6" stroke-width="2"/>
      <circle cx="60" cy="50" r="25" fill="#8b5cf6"/>
      <text x="60" y="58" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">👥</text>
      
      <text x="110" y="35" font-family="Microsoft YaHei" font-size="22" fill="#7c3aed" font-weight="bold">
        人口统计学
      </text>
      <text x="110" y="60" font-family="Microsoft YaHei" font-size="18" fill="#6b21a8">
        年龄、性别、职业、收入、教育
      </text>
      <text x="110" y="80" font-family="Microsoft YaHei" font-size="18" fill="#6b21a8">
        家庭状况、居住地等
      </text>
      
      <!-- 心理生活方式 -->
      <rect x="420" y="0" width="400" height="100" fill="#f0fdf4" rx="15" stroke="#10b981" stroke-width="2"/>
      <circle cx="480" cy="50" r="25" fill="#10b981"/>
      <text x="480" y="58" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">🧠</text>
      
      <text x="530" y="35" font-family="Microsoft YaHei" font-size="22" fill="#059669" font-weight="bold">
        心理生活方式
      </text>
      <text x="530" y="60" font-family="Microsoft YaHei" font-size="18" fill="#047857">
        价值观、兴趣爱好、生活态度
      </text>
      <text x="530" y="80" font-family="Microsoft YaHei" font-size="18" fill="#047857">
        消费理念、品牌偏好等
      </text>
      
      <!-- 消费行为 -->
      <rect x="840" y="0" width="400" height="100" fill="#fefbf3" rx="15" stroke="#f59e0b" stroke-width="2"/>
      <circle cx="900" cy="50" r="25" fill="#f59e0b"/>
      <text x="900" y="58" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">🛒</text>
      
      <text x="950" y="35" font-family="Microsoft YaHei" font-size="22" fill="#d97706" font-weight="bold">
        消费行为
      </text>
      <text x="950" y="60" font-family="Microsoft YaHei" font-size="18" fill="#92400e">
        购买习惯、决策过程
      </text>
      <text x="950" y="80" font-family="Microsoft YaHei" font-size="18" fill="#92400e">
        使用频率、支付偏好等
      </text>
      
      <!-- 地理场景 -->
      <rect x="1260" y="0" width="400" height="100" fill="#f0f9ff" rx="15" stroke="#3b82f6" stroke-width="2"/>
      <circle cx="1320" cy="50" r="25" fill="#3b82f6"/>
      <text x="1320" y="58" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">📍</text>
      
      <text x="1370" y="35" font-family="Microsoft YaHei" font-size="22" fill="#1d4ed8" font-weight="bold">
        地理场景
      </text>
      <text x="1370" y="60" font-family="Microsoft YaHei" font-size="18" fill="#1e40af">
        地域分布、使用场景
      </text>
      <text x="1370" y="80" font-family="Microsoft YaHei" font-size="18" fill="#1e40af">
        渠道偏好、接触点等
      </text>
    </g>
  </g>
  
  <!-- 关键强调 -->
  <rect x="200" y="700" width="1520" height="100" fill="#fef2f2" rx="20" opacity="0.9"/>
  <text x="960" y="735" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#dc2626" font-weight="bold">
    关键：画像要基于真实数据和洞察，而非主观臆断
  </text>
  <text x="960" y="770" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" fill="#991b1b">
    数据驱动的画像才能指导有效的营销决策
  </text>
  
  <!-- 装饰性Persona卡片示例 -->
  <g transform="translate(1600,750)">
    <rect x="0" y="0" width="120" height="160" fill="white" stroke="#64748b" stroke-width="2" rx="10"/>
    <circle cx="60" cy="40" r="20" fill="#e2e8f0"/>
    <rect x="20" y="70" width="80" height="8" fill="#cbd5e1" rx="4"/>
    <rect x="20" y="85" width="60" height="6" fill="#e2e8f0" rx="3"/>
    <rect x="20" y="100" width="70" height="6" fill="#e2e8f0" rx="3"/>
    <rect x="20" y="115" width="50" height="6" fill="#e2e8f0" rx="3"/>
    <rect x="20" y="130" width="80" height="6" fill="#e2e8f0" rx="3"/>
    
    <text x="60" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#64748b">
      Persona卡片
    </text>
  </g>
  
  <!-- 底部总结 -->
  <rect x="300" y="830" width="1320" height="60" fill="#f0f9ff" rx="15" opacity="0.8"/>
  <text x="960" y="865" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" fill="#1d4ed8" font-weight="bold">
    用户画像是精准营销的科学基础，让我们从"猜测"走向"洞察"
  </text>
</svg>
