<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 背景装饰弧线 -->
  <path d="M0,100 Q480,50 960,75 T1920,50" stroke="#fde68a" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,1000 Q480,950 960,975 T1920,950" stroke="#e0e7ff" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#f59e0b">
    银发族画像：渴望连接、需要帮助、注重安心
  </text>
  
  <!-- 王大爷Persona卡片 -->
  <g transform="translate(100,130)">
    <!-- 卡片背景 -->
    <rect x="0" y="0" width="1720" height="700" fill="#fffbeb" rx="20" stroke="#f59e0b" stroke-width="3"/>
    
    <!-- 头像区域 -->
    <circle cx="120" cy="120" r="60" fill="#f59e0b"/>
    <text x="120" y="135" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="white" font-weight="bold">王</text>
    
    <!-- 基本信息 -->
    <text x="220" y="80" font-family="Microsoft YaHei" font-size="32" fill="#d97706" font-weight="bold">
      王大爷 (银发族典型代表)
    </text>
    <text x="220" y="115" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      年龄：65-75岁 | 职业：退休 | 居住：二三线城市或县城
    </text>
    <text x="220" y="145" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      健康状况：基本健康，有慢性病 | 数字技能：初级，需要帮助
    </text>
    <text x="220" y="175" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      家庭：与老伴同住或独居，子女在外地工作
    </text>
    
    <!-- 典型标签 -->
    <text x="50" y="240" font-family="Microsoft YaHei" font-size="28" fill="#d97706" font-weight="bold">
      典型标签：
    </text>
    
    <g transform="translate(50,260)">
      <!-- 标签行1 -->
      <rect x="0" y="0" width="120" height="35" fill="#f59e0b" rx="17"/>
      <text x="60" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#触网新手</text>
      
      <rect x="140" y="0" width="120" height="35" fill="#d97706" rx="17"/>
      <text x="200" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#价格敏感</text>
      
      <rect x="280" y="0" width="120" height="35" fill="#92400e" rx="17"/>
      <text x="340" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#信任线下</text>
      
      <rect x="420" y="0" width="120" height="35" fill="#a16207" rx="17"/>
      <text x="480" y="23" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#健康关注</text>
      
      <!-- 标签行2 -->
      <rect x="0" y="50" width="120" height="35" fill="#3b82f6" rx="17"/>
      <text x="60" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#亲情沟通</text>
      
      <rect x="140" y="50" width="120" height="35" fill="#ef4444" rx="17"/>
      <text x="200" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#防骗意识</text>
      
      <rect x="280" y="50" width="120" height="35" fill="#10b981" rx="17"/>
      <text x="340" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#学习意愿</text>
      
      <rect x="420" y="50" width="120" height="35" fill="#8b5cf6" rx="17"/>
      <text x="480" y="73" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white" font-weight="bold">#时间充裕</text>
    </g>
    
    <!-- 核心需求 -->
    <text x="50" y="370" font-family="Microsoft YaHei" font-size="28" fill="#d97706" font-weight="bold">
      核心需求：
    </text>
    <text x="50" y="405" font-family="Microsoft YaHei" font-size="24" fill="#92400e" font-weight="bold">
      操作要简单！资费要便宜！服务要耐心！
    </text>
    <text x="50" y="435" font-family="Microsoft YaHei" font-size="24" fill="#92400e" font-weight="bold">
      使用要安全！联系子女要方便！
    </text>
    <text x="50" y="470" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 手机功能简单易懂，字体要大，操作要直观
    </text>
    <text x="50" y="500" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 套餐资费透明，不要有隐藏消费，月租要低
    </text>
    <text x="50" y="530" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 客服要有耐心，能反复解释，最好有专门的老年服务
    </text>
    
    <!-- 痛点聚焦 -->
    <text x="900" y="240" font-family="Microsoft YaHei" font-size="28" fill="#dc2626" font-weight="bold">
      痛点聚焦：
    </text>
    <text x="900" y="275" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      手机/APP不会用！
    </text>
    <text x="900" y="305" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      套餐/账单看不懂！
    </text>
    <text x="900" y="335" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      怕乱扣费怕被骗！
    </text>
    <text x="900" y="365" font-family="Microsoft YaHei" font-size="24" fill="#ef4444" font-weight="bold">
      客服找不到/说不清！
    </text>
    
    <!-- 信息渠道 -->
    <text x="900" y="420" font-family="Microsoft YaHei" font-size="28" fill="#d97706" font-weight="bold">
      信息渠道：
    </text>
    <text x="900" y="455" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 电视报纸：传统媒体仍是主要信息来源
    </text>
    <text x="900" y="485" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 营业厅：面对面服务，信任度最高
    </text>
    <text x="900" y="515" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 社区宣传：邻里推荐，口碑传播
    </text>
    <text x="900" y="545" font-family="Microsoft YaHei" font-size="22" fill="#92400e">
      • 子女告知：最信任的信息来源
    </text>
    
    <!-- 孝心卡示意图 -->
    <g transform="translate(1400,350)">
      <rect x="0" y="0" width="200" height="120" fill="#fef3c7" rx="15" stroke="#f59e0b" stroke-width="2"/>
      <text x="100" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#d97706" font-weight="bold">
        孝心卡/银龄卡
      </text>
      
      <!-- 特色功能 -->
      <text x="20" y="50" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 大字体界面
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 一键求助
      </text>
      <text x="20" y="90" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 亲情号码
      </text>
      <text x="20" y="110" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 防骗提醒
      </text>
      
      <text x="120" y="50" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 健康管理
      </text>
      <text x="120" y="70" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 专属客服
      </text>
      <text x="120" y="90" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 优惠资费
      </text>
      <text x="120" y="110" font-family="Microsoft YaHei" font-size="14" fill="#92400e">
        ✓ 上门服务
      </text>
    </g>
    
    <!-- 互动提问 -->
    <rect x="50" y="580" width="1620" height="100" fill="#fef3c7" rx="15" opacity="0.8"/>
    <text x="80" y="615" font-family="Microsoft YaHei" font-size="26" fill="#d97706" font-weight="bold">
      互动提问：
    </text>
    <text x="80" y="650" font-family="Microsoft YaHei" font-size="24" fill="#92400e">
      "我们为王大爷这样的老年用户提供服务，最重要的原则是什么？"
    </text>
    <text x="80" y="680" font-family="Microsoft YaHei" font-size="22" fill="#a16207">
      (强调同理心、耐心和尊重，介绍联通的适老化服务举措)
    </text>
  </g>
  
  <!-- 装饰性老年人图标 -->
  <g transform="translate(1650,900)">
    <!-- 老人拐杖 -->
    <line x1="0" y1="0" x2="0" y2="40" stroke="#92400e" stroke-width="4" stroke-linecap="round"/>
    <path d="M0,0 Q-10,-5 -15,0" stroke="#92400e" stroke-width="3" fill="none"/>
    
    <!-- 手机 -->
    <rect x="20" y="10" width="25" height="40" fill="#f59e0b" rx="5"/>
    <rect x="23" y="15" width="19" height="25" fill="#fef3c7" rx="2"/>
    <circle cx="32" cy="45" r="2" fill="#fef3c7"/>
    
    <!-- 放大镜 -->
    <circle cx="60" cy="25" r="12" fill="none" stroke="#92400e" stroke-width="2"/>
    <line x1="70" y1="35" x2="78" y2="43" stroke="#92400e" stroke-width="2"/>
  </g>
</svg>
